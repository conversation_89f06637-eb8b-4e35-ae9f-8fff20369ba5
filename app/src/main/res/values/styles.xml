<resources>

    <style name="SplashTheme" parent="BukuTheme">
        <item name="android:windowBackground">@drawable/splash_edc</item>
        <item name="colorPrimary">@color/independence_day_color</item>
        <item name="colorPrimaryDark">@color/independence_day_color</item>
        <item name="colorAccent">@color/independence_day_color</item>
    </style>

    <!-- Zoho SalesIQ Custom Theme -->
    <style name="BukuWarungZohoTheme" parent="Theme.SalesIQ.Light.DarkActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimary</item>
        <item name="colorAccent">@color/colorPrimary</item>
        <item name="android:statusBarColor">@color/colorPrimary</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:navigationBarColor">@color/colorPrimary</item>
        <!-- Force status bar color for all activities -->
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
        <!-- Additional theme attributes for better compatibility -->
        <item name="android:colorPrimary">@color/colorPrimary</item>
        <item name="android:colorPrimaryDark">@color/colorPrimary</item>
        <item name="android:colorAccent">@color/colorPrimary</item>
    </style>

    <!-- Required base theme for Zoho SalesIQ -->
    <style name="Theme.SalesIQ.Base" parent="BukuWarungZohoTheme">
        <!-- This will be used by Zoho SDK automatically -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimary</item>
        <item name="android:statusBarColor">@color/colorPrimary</item>
    </style>

    <!-- Base application theme. -->
    <style name="BukuTheme" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="android:windowContentOverlay">@null</item>
        <item name="actionBarStyle">@style/AppBarStyle</item>
        <item name="colorAccent">@color/colorPrimary</item>
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimary</item>
        <item name="android:datePickerStyle">@style/DatePickerThemeNew</item>
        <item name="android:textViewStyle">@style/Body3</item>
        <item name="bottomSheetDialogTheme">@style/BottomSheetDialogTheme</item>
        <!--<item name="preferenceTheme">@style/PreferenceThemeOverlay</item>-->
    </style>


    <style name="DatePickerTheme" parent="Theme.MaterialComponents.Light.Dialog.Alert">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:buttonBarNegativeButtonStyle">
            @style/DatePickerTheme.ButtonBarNegativeButtonStyle
        </item>
    </style>

    <style name="DatePickerTheme.ButtonBarNegativeButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@android:color/holo_red_light</item>
    </style>

    <style name="AppBarStyle">
        <item name="elevation">0dp</item>
    </style>

    <!-- Dialog Styles -->
    <style name="DefaultDialogTheme" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:windowBackground">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
    </style>

    <style name="FullScreenDialogTheme" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:windowBackground">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowMinWidthMajor">90%</item>
        <item name="android:windowMinWidthMinor">90%</item>
    </style>

    <style name="DialogAnimation">
        <item name="android:windowEnterAnimation">@anim/slide_up</item>
        <item name="android:windowExitAnimation">@anim/slide_down</item>
    </style>

    <style name="DatePickerDialogParent" parent="android:Theme.DeviceDefault.Light.Dialog">
        <item name="android:datePickerStyle">@style/DatePickerThemeNew</item>
    </style>

    <style name="DatePickerThemeNew" parent="@android:style/Widget.Material.Light.DatePicker">
        <item name="android:headerBackground">@color/green_80</item>
        <item name="colorAccent">@color/green_80</item>
        <item name="colorControlActivated">@color/blue_bg_datepicker</item>
        <item name="colorControlHighlight">@color/blue_bg_datepicker</item>
        <item name="android:positiveButtonText">@string/save</item>
    </style>

    <!--    Styles Information for TimePicker used in Self Reminder-->
    <style name="TimePickerTheme" parent="Theme.AppCompat.Dialog.Alert">
        <item name="title">false</item>
        <item name="android:textSize">28sp</item>
        <item name="android:textColorPrimary">@color/black_80</item>
        <item name="android:textColorSecondary">@color/hint_color</item>
    </style>
    <!-- End of Dialog Styles -->

    <style name="OutlineTextInputStyle" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense">
        <item name="boxStrokeColor">@color/outline_text_input_color</item>
        <item name="boxStrokeWidth">1dp</item>
        <item name="colorControlActivated">@color/black</item>
        <item name="passwordToggleDrawable">@null</item>
    </style>

    <style name="OutlineTextInputStyle.WithColorActivated" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense">
        <item name="boxStrokeColor">@color/outline_text_input_color</item>
        <item name="boxStrokeWidth">1dp</item>
        <item name="colorControlActivated">@color/colorPrimary</item>
        <item name="passwordToggleDrawable">@null</item>
    </style>

    <style name="NoLineTextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.FilledBox">
        <item name="colorControlNormal">@color/black_10</item>
        <item name="colorControlActivated">@color/black_10</item>
        <item name="colorControlHighlight">@color/black_10</item>
        <item name="passwordToggleDrawable">@null</item>
    </style>

    <style name="DefaultRadioButtonTheme" parent="Theme.AppCompat.Light">
        <item name="colorControlNormal">@color/colorPrimary</item>
        <item name="colorControlActivated">@color/colorPrimary</item>
    </style>

    <style name="RadioButtonStyle" parent="Theme.AppCompat.Light">
        <item name="colorControlNormal">@color/black_20</item>
        <item name="colorControlActivated">@color/blue_60</item>
    </style>

    <style name="DatePickerDialog" parent="@style/Theme.AppCompat.Light.Dialog">
        <item name="android:windowNoTitle">false</item>
    </style>

    <style name="ActionBarAppTheme" parent="Theme.MaterialComponents.DayNight.DarkActionBar.Bridge">
        <item name="android:textColorSecondary">@color/light_grey</item>
        <item name="colorAccent">@color/grab</item>
        <item name="colorPrimary">@color/grab</item>
        <item name="colorPrimaryDark">@color/grab</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1
        </item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2
        </item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3
        </item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4
        </item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5
        </item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6
        </item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1
        </item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2
        </item>
        <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
        <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
        <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
        <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
    </style>

    <style name="NoActionBarAppTheme" parent="Theme.MaterialComponents.DayNight.NoActionBar.Bridge">
        <item name="android:textColorSecondary">@color/light_grey</item>
        <item name="colorAccent">@color/grab</item>
        <item name="colorPrimary">@color/grab</item>
        <item name="colorPrimaryDark">@color/grab</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1
        </item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2
        </item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3
        </item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4
        </item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5
        </item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6
        </item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1
        </item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2
        </item>
        <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
        <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
        <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
        <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
    </style>

    <style name="ContactTextStyle">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">#4d4d4d</item>
    </style>

    <style name="BukuTheme.Title">
        <item name="android:fontFamily">@font/roboto</item>
        <item name="android:textSize">20sp</item>
    </style>

    <style name="ThemeOverlay.FilterButton" parent="ThemeOverlay.AppCompat.Dark">
        <item name="colorButtonNormal">@color/colorPrimary</item>
    </style>

    <style name="BukuTheme.TextDivider">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginLeft">4dp</item>
        <item name="android:layout_marginRight">4dp</item>
        <item name="android:textColor">@color/greySecondary</item>
    </style>

    <style name="BukuTheme.Body1">
        <item name="android:fontFamily">@font/roboto</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="BukuTheme.Caption">
        <item name="android:fontFamily">@font/roboto</item>
        <item name="android:textSize">12sp</item>
    </style>

    <style name="BukuTheme.Subheader">
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textSize">16sp</item>
    </style>

    <style name="StickerPreviewButtonText" parent="Widget.MaterialComponents.Button.TextButton">
        <item name="android:textColor">@android:color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:singleLine">true</item>
        <item name="android:ellipsize">end</item>
        <item name="android:textAllCaps">true</item>
    </style>

    <style name="sticker_packs_list_item_author_style">
        <item name="android:gravity">center_vertical</item>
        <item name="android:maxLines">1</item>
        <item name="android:singleLine">true</item>
        <item name="android:lines">1</item>
        <item name="android:textSize">12sp</item>
    </style>

    <style name="ToolbarTheme">
        <item name="android:background">@color/colorPrimary</item>
        <item name="colorControlNormal">@color/white</item>
        <item name="android:titleTextAppearance">@style/Heading2</item>
        <item name="titleTextColor">@color/white</item>
    </style>

    <style name="Base.TextAppearance.AppCompat.Tooltip">
        <item name="android:textSize">14sp</item>
    </style>

    <style name="StyleEditTextApperanceBig">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">#4d4d4d</item>
    </style>

    <style name="StyleEditTextApperanceBizCard">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">#4d4d4d</item>
    </style>

    <style name="harian">

        <item name="android:textSize">
            14sp
        </item>
        <item name="android:textColor">
            @color/white
        </item>

    </style>

    <style name="harian_unselected">

        <item name="android:textSize">
            14sp
        </item>

        <item name="android:textColor">
            @color/filter_disable
        </item>

    </style>

    <style name="tut_heading">
        <item name="android:textSize">18sp</item>
        <item name="android:fontFamily">@font/roboto</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">#222222</item>
    </style>

    <style name="tut_body">
        <item name="android:textSize">15sp</item>
        <item name="android:fontFamily">@font/roboto</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">#666666</item>
    </style>

    <style name="PadButtonStyle.Numeric.Green" parent="@style/PadButtonStyle.Numeric">
        <item name="android:background">@color/colorSuccessButton</item>
    </style>

    <style name="PadButtonStyle.Numeric.Operator" parent="@style/PadButtonStyle.Numeric">
        <item name="android:background">@drawable/bg_button</item>
    </style>

    <style name="PadButtonStyle" parent="@style/Base.Widget.AppCompat.Button.Borderless">
        <item name="android:gravity">center</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:foreground">?android:selectableItemBackground</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="PadButtonStyle.Numeric" parent="@style/PadButtonStyle">
        <item name="android:textSize">20.0dp</item>
        <item name="android:layout_margin">0.3dp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:fontFamily">@font/roboto</item>
        <item name="android:textColor">@color/colorInversePrimaryText</item>
        <item name="android:background">@drawable/bg_button</item>
    </style>

    <style name="Body1Sans">
        <item name="android:textSize">14sp</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textStyle">normal</item>
        <item name="android:lineSpacingExtra">6sp</item>
        <item name="android:textColor">#666666</item>
    </style>

    <style name="spinner_dialog">
        <item name="android:background">@android:color/white</item>
        <item name="android:textColor">@color/body_text</item>
        <item name="android:padding">0dp</item>
    </style>

    <!-- popup menu -->
    <style name="PopupMenu" parent="@android:style/Widget.PopupMenu">
        <item name="android:textColor">@color/by_date_category</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:fontFamily">@font/roboto</item>
        <item name="android:itemBackground">#FFFBFA</item>
    </style>

    <style name="DatePopupMenu" parent="@android:style/Widget.PopupMenu">
        <item name="android:textColor">@color/by_date_category</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:fontFamily">@font/roboto</item>
        <item name="android:itemBackground">#FFFBFA</item>
    </style>

    <style name="DefaultMaterialButtonStyle" parent="Widget.MaterialComponents.Button.UnelevatedButton">
        <item name="backgroundTint">@color/btn_color_state</item>
    </style>

    <style name="DefaultMaterialButtonStyleAdjacent">
        <item name="backgroundTint">@color/btn_color_state</item>
    </style>

    <style name="DisableMaterialButtonStyleAdjacent">
        <item name="backgroundTint">@color/black_20</item>
    </style>

    <style name="EditText.VerificationCode" parent="Base.Widget.AppCompat.EditText">
        <item name="android:theme">@style/TextInputLayout.Grey</item>
        <item name="android:paddingTop">8dp</item>
        <item name="android:paddingBottom">8dp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">22sp</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginLeft">96dp</item>
        <item name="android:layout_marginTop">16dp</item>
        <item name="android:layout_marginBottom">16dp</item>
        <item name="android:layout_marginRight">96dp</item>
        <item name="android:background">@drawable/bg_verification_code_edit_text</item>
        <item name="android:inputType">number</item>
        <item name="android:maxLength">4</item>
        <item name="android:hint">* * * *</item>
        <item name="android:textColorHint">@color/primary_material_dark</item>
        <item name="android:singleLine">true</item>
        <item name="android:textColor">@color/primary_material_dark</item>
    </style>

    <style name="TextInputLayout.Grey" parent="Widget.Design.TextInputLayout">
        <item name="android:textColor">@color/primary_material_dark</item>
        <item name="colorControlNormal">@color/primary_dark_material_dark</item>
        <item name="colorControlActivated">@color/primary_dark_material_dark</item>
        <item name="colorControlHighlight">@color/primary_dark_material_dark</item>
        <item name="colorAccent">@color/primary_dark_material_dark</item>
        <item name="android:textColorHint">@color/primary_dark_material_light</item>
        <item name="android:textColorHighlight">@color/primary_dark_material_light</item>
        <item name="android:textColorLink">@color/primary_dark_material_light</item>
        <item name="passwordToggleDrawable">@null</item>
    </style>

    <style name="TextInputLayout.Grey.TextAppearance" parent="@android:style/TextAppearance">
        <item name="android:textColor">@color/red_error</item>
        <item name="passwordToggleDrawable">@null</item>
    </style>

    <style name="SearchTextInputLayoutStyle" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense">
        <item name="boxStrokeColor">@color/search_input_box_stroke</item>
        <item name="boxBackgroundColor">@color/white</item>
        <item name="boxCornerRadiusBottomEnd">@dimen/_10dp</item>
        <item name="boxCornerRadiusBottomStart">@dimen/_10dp</item>
        <item name="boxCornerRadiusTopEnd">@dimen/_10dp</item>
        <item name="boxCornerRadiusTopStart">@dimen/_10dp</item>
    </style>

    <style name="ShadowBottom">
        <item name="android:layout_width">match_parent</item>
        <item name="android:background">@drawable/shadow_bottom</item>
        <item name="android:alpha">0.8</item>
    </style>

    <style name="ShadowTop">
        <item name="android:layout_width">match_parent</item>
        <item name="android:background">@drawable/shadow_top</item>
        <item name="android:alpha">0.8</item>
    </style>

    <style name="Divider">
        <item name="android:background">#EAEAEA</item>
    </style>

    <style name="Divider.Black5">
        <item name="android:background">@color/black_5</item>
        <item name="android:layout_height">1dp</item>
        <item name="android:layout_width">match_parent</item>
    </style>

    <style name="Divider.Horizontal">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">1dp</item>
    </style>

    <style name="Divider.Horizontal.LightBlue">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">1dp</item>
        <item name="android:background">#EAF6FF</item>
    </style>

    <style name="DialogTheme" parent="@style/ThemeOverlay.AppCompat.Dialog">
        <item name="android:windowTranslucentStatus">true</item>
    </style>

    <style name="RoundDialog" parent="DialogTheme">
        <item name="cornerRadius">@dimen/_16dp</item>
    </style>

    <style name="Button" parent="Widget.MaterialComponents.Button.UnelevatedButton" />

    <style name="Button.OutlinePrimary" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="strokeColor">@color/colorPrimary</item>
    </style>

    <style name="Button.OutlineRed" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="strokeColor">@color/red_80</item>
    </style>

    <style name="Button.OutlinePrimary.Bold" parent="Button.OutlinePrimary">
        <item name="fontFamily">@font/roboto_bold</item>
    </style>

    <style name="Button.Outline" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="strokeColor">@color/black_40</item>
        <item name="android:textColor">@color/black</item>
    </style>

    <style name="Button.Text" parent="Widget.MaterialComponents.Button.TextButton">
        <item name="android:textColor">@color/colorPrimary</item>
        <item name="textAllCaps">false</item>
    </style>

    <style name="BottomSheetDialogTheme" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="backgroundColor">@color/fui_transparent</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="colorAccent">@color/colorPrimary</item>
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
    </style>

    <style name="BottomSheet" parent="@style/Widget.Design.BottomSheet.Modal">
        <item name="android:background">@drawable/rounded_corner_background_bottom_sheet</item>
    </style>

    <style name="BaseBottomSheetDialog" parent="@style/Theme.Design.Light.BottomSheetDialog">
        <item name="android:windowIsFloating">false</item>
        <item name="bottomSheetStyle">@style/BottomSheet</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
    </style>

    <style name="BottomSheetDialogThemeRound" parent="BaseBottomSheetDialog" />

    <style name="BottomSheetModal.RoundCorner" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@drawable/bg_top_rounded_corner</item>
    </style>

    <style name="BottomSheetDialogTheme.RoundCorner">
        <item name="bottomSheetStyle">@style/BottomSheetModal.RoundCorner</item>
    </style>

    <style name="TabLayout.Theme.Primary" parent="Widget.MaterialComponents.TabLayout">
        <item name="android:background">@color/colorPrimary</item>
        <item name="tabIndicatorColor">@color/white</item>
        <item name="tabTextColor">@color/white</item>
        <item name="tabRippleColor">@color/colorPrimary</item>
        <item name="tabIndicatorHeight">3dp</item>
        <item name="tabMode">scrollable</item>
    </style>

    <style name="TabLayout.Theme.PrimaryNew" parent="Widget.MaterialComponents.TabLayout">
        <item name="android:background">@color/white</item>
        <item name="tabIndicatorColor">@color/colorPrimary</item>
        <item name="tabTextColor">@color/black_40</item>
        <item name="tabSelectedTextColor">@color/colorPrimary</item>
        <item name="tabRippleColor">@color/colorPrimary</item>
        <item name="tabIndicatorHeight">3dp</item>
        <item name="tabMode">scrollable</item>
    </style>

    <style name="Tab.Theme.Primary.TextAppearance" parent="TextAppearance.Design.Tab">
        <item name="android:textSize">14sp</item>
        <item name="fontFamily">@font/roboto_bold</item>
        <item name="textAllCaps">false</item>
    </style>

    <style name="CashFilterChipStyle">
        <item name="chipBackgroundColor">@color/cash_chip_color_state</item>
        <item name="android:textColor">@color/cash_chip_text_color_state</item>
        <item name="android:checkable">true</item>
        <item name="android:clickable">true</item>
    </style>

    <style name="UtangFilterChipStyle">
        <item name="chipBackgroundColor">@color/utang_chip_color_state</item>
        <item name="android:textColor">@color/utang_chip_text_color_state</item>

        <item name="android:checkable">true</item>
        <item name="checkedIconEnabled">false</item>
        <item name="android:clickable">true</item>
    </style>

    <style name="PpobFilterChipStyle">
        <item name="chipBackgroundColor">@color/ppob_chip_color_state</item>
        <item name="android:textColor">@color/ppob_chip_text_color_state</item>
        <item name="chipStrokeColor">@color/ppob_chip_stroke_tint_new</item>
        <item name="android:checkable">true</item>
        <item name="checkedIconEnabled">false</item>
        <item name="android:clickable">true</item>
    </style>

    <style name="CategoryFilterChipStyle">
        <item name="chipBackgroundColor">@color/utang_chip_color_state</item>
        <item name="chipStrokeColor">@color/category_chip_stroke</item>
        <item name="strokeWidth">@dimen/_2dp</item>
        <item name="android:textColor">@color/utang_chip_text_color_state</item>
        <item name="android:checkable">true</item>
        <item name="checkedIconEnabled">false</item>
        <item name="android:clickable">true</item>
    </style>

    <style name="CustomCalendarStyle" parent="ThemeOverlay.MaterialComponents.MaterialCalendar">
        <item name="android:colorPrimary">@color/colorPrimary</item>
    </style>

    <style name="CalenderViewDate" parent="android:TextAppearance.DeviceDefault.Small">
        <item name="android:textColor">@color/black_80</item>
        <item name="android:weekNumberColor">@color/black_80</item>
        <item name="android:fontFamily">@font/roboto</item>
        <item name="android:textSize">@dimen/text_12sp</item>
    </style>

    <style name="CalendarViewStyle" parent="BukuTheme">
        <item name="android:textColorPrimary">@color/black_80</item>
        <item name="colorControlActivated">@color/blue_bg_datepicker</item>
        <item name="android:fontFamily">@font/roboto_bold</item>
        <item name="android:textSize">@dimen/text_14sp</item>
    </style>

    <!--Text Styles-->
    <style name="BaseTextView" parent="Widget.AppCompat.TextView">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:fontFamily">@font/roboto</item>
        <item name="android:lineSpacingExtra">6sp</item>
        <item name="android:textColor">@color/black_80</item>
    </style>

    <style name="BaseHeading" parent="BaseTextView">
        <item name="android:fontFamily">@font/roboto_bold</item>
    </style>

    <style name="TootleTitle" parent="BaseTextView">
        <item name="android:fontFamily">@font/roboto_bold</item>
        <item name="android:textSize">@dimen/text_18sp</item>
        <item name="android:ellipsize">end</item>
        <item name="android:maxLines">1</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="Heading1" parent="BaseHeading">
        <item name="android:textSize">@dimen/text_24sp</item>
        <item name="android:lineSpacingExtra">8sp</item>
    </style>

    <style name="Heading2" parent="BaseHeading">
        <item name="android:textSize">@dimen/text_18sp</item>
    </style>

    <style name="Heading3" parent="BaseHeading">
        <item name="android:textSize">@dimen/text_16sp</item>
    </style>

    <style name="SubHeading1" parent="BaseHeading">
        <item name="android:textSize">@dimen/text_14sp</item>
    </style>

    <style name="SubHeading2" parent="BaseHeading">
        <item name="android:textSize">@dimen/text_12sp</item>
    </style>

    <style name="Body1" parent="BaseTextView">
        <item name="android:textSize">@dimen/text_16sp</item>
    </style>

    <style name="Body2" parent="BaseTextView">
        <item name="android:textSize">@dimen/text_14sp</item>
    </style>

    <style name="Body3" parent="BaseTextView">
        <item name="android:textSize">@dimen/text_12sp</item>
    </style>

    <style name="Body4" parent="Body3">
        <item name="android:textColor">@color/black_40</item>
    </style>

    <style name="Body5" parent="Body3">
        <item name="android:textColor">@color/black_60</item>
    </style>

    <style name="Label1" parent="BaseTextView">
        <item name="android:textSize">@dimen/text_12sp</item>
    </style>

    <style name="Label2" parent="BaseTextView">
        <item name="android:textSize">@dimen/text_10sp</item>
    </style>

    <style name="Label3" parent="BaseTextView">
        <item name="android:textSize">@dimen/text_10sp</item>
        <item name="android:fontFamily">@font/roboto_bold</item>
    </style>

    <style name="Caption" parent="BaseTextView">
        <item name="android:textSize">@dimen/text_8sp</item>
    </style>

    <style name="Body3.black40" parent="Body3">
        <item name="android:textColor">@color/black_40</item>
    </style>

    <style name="Body3.black60" parent="Body3">
        <item name="android:textColor">@color/black_60</item>
    </style>

    <style name="Body3.green80" parent="Body3">
        <item name="android:textColor">@color/green_80</item>
    </style>

    <style name="Body3.red80Bold" parent="Body3">
        <item name="android:textColor">@color/red_80</item>
        <item name="android:fontFamily">@font/roboto_bold</item>
    </style>

    <style name="SubHeading1.red80" parent="SubHeading1">
        <item name="android:textColor">@color/red_80</item>
    </style>

    <style name="SubHeading1.green80" parent="SubHeading1">
        <item name="android:textColor">@color/green_80</item>
    </style>

    <style name="SubHeading1.black60" parent="SubHeading1">
        <item name="android:textColor">@color/black_60</item>
    </style>

    <style name="SubHeading1.black40" parent="SubHeading1">
        <item name="android:textColor">@color/black_40</item>
    </style>

    <style name="SubHeading1.black80" parent="SubHeading1">
        <item name="android:textColor">@color/black_80</item>
    </style>

    <style name="ButtonFill" parent="Widget.MaterialComponents.Button.TextButton.Icon">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:paddingBottom">11dp</item>
        <item name="android:paddingTop">11dp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textAppearance">@style/Heading3</item>
        <item name="android:textColor">@color/btn_text_color_state</item>
        <item name="backgroundTint">@color/btn_color_state</item>
        <item name="iconGravity">textStart</item>
        <item name="iconTint">@null</item>
    </style>

    <style name="ButtonFillNoPadding" parent="Widget.MaterialComponents.Button.TextButton.Icon">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textAppearance">@style/Heading3</item>
        <item name="android:textColor">@color/btn_text_color_state</item>
        <item name="backgroundTint">@color/btn_color_state</item>
        <item name="iconGravity">textStart</item>
        <item name="iconTint">@null</item>
    </style>

    <style name="ButtonFill.Blue" parent="ButtonFill">
        <item name="android:textColor">@color/white</item>
        <item name="backgroundTint">@color/colorPrimary</item>
    </style>

    <style name="ButtonFill.Green80" parent="ButtonFill">
        <item name="android:textColor">@color/white</item>
        <item name="backgroundTint">@color/green_80</item>
    </style>

    <style name="ButtonFill.Red" parent="ButtonFill">
        <item name="android:textColor">@color/white</item>
        <item name="backgroundTint">@color/red_80</item>
    </style>

    <style name="ButtonFill.Blue.Bold" parent="ButtonFill.Blue">
        <item name="fontFamily">@font/roboto_bold</item>
    </style>

    <style name="ButtonFill.Blue80" parent="ButtonFill">
        <item name="android:textColor">@color/white</item>
        <item name="backgroundTint">@color/btn_blue80_color_state</item>
        <item name="android:textAppearance">@style/SubHeading1</item>
    </style>

    <style name="ButtonFill.Blue60" parent="ButtonFill">
        <item name="android:textColor">@color/white</item>
        <item name="backgroundTint">@color/btn_blue60_color_state</item>
        <item name="android:textAppearance">@style/SubHeading1</item>
    </style>

    <style name="ButtonOutline.White.Text" parent="ButtonOutline">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">@dimen/text_14sp</item>
        <item name="fontFamily">@font/roboto_bold</item>
        <item name="strokeColor">@color/white</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:paddingBottom">13dp</item>
        <item name="android:paddingTop">13dp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textAppearance">@style/Heading3</item>
    </style>

    <style name="ButtonOutline.Red" parent="ButtonOutline">
        <item name="android:textColor">@color/red_60</item>
        <item name="android:textAppearance">@style/SubHeading1</item>
        <item name="strokeColor">@color/red_60</item>
    </style>

    <style name="ButtonOutline.Black" parent="ButtonOutline">
        <item name="android:textColor">@color/black_80</item>
        <item name="strokeColor">@color/black_80</item>
    </style>

    <style name="ButtonOutline.White" parent="ButtonOutline">
        <item name="android:textColor">@color/black_60</item>
        <item name="android:textSize">@dimen/text_12sp</item>
        <item name="fontFamily">@font/roboto_bold</item>
        <item name="strokeColor">@color/black_60</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:paddingBottom">13dp</item>
        <item name="android:paddingTop">13dp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textAppearance">@style/Heading3</item>
    </style>

    <style name="ButtonOutline.WhiteAll" parent="ButtonOutline">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">@dimen/text_12sp</item>
        <item name="fontFamily">@font/roboto_bold</item>
        <item name="strokeColor">@color/white</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:paddingBottom">13dp</item>
        <item name="android:paddingTop">13dp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textAppearance">@style/Heading3</item>
    </style>

    <style name="ButtonOutline1" parent="Widget.MaterialComponents.Button.OutlinedButton.Icon">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textAppearance">@style/Heading3</item>
        <item name="android:textColor">@color/web_orange</item>
        <item name="strokeColor">@color/web_orange</item>
        <item name="cornerRadius">4dp</item>
        <item name="iconGravity">textStart</item>
    </style>

    <style name="ButtonOutline.Blue1" parent="ButtonOutline1">
        <item name="android:textColor">@color/colorPrimary</item>
        <item name="strokeColor">@color/colorPrimary</item>
    </style>

    <style name="Toolbar">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">?attr/actionBarSize</item>
        <item name="android:background">@color/colorPrimary</item>
        <item name="android:contentInsetLeft">0dp</item>
        <item name="android:contentInsetStart">0dp</item>
        <item name="contentInsetLeft">0dp</item>
        <item name="contentInsetStart">0dp</item>
        <item name="android:contentInsetRight">0dp</item>
        <item name="android:contentInsetEnd">0dp</item>
        <item name="contentInsetRight">0dp</item>
        <item name="contentInsetEnd">0dp</item>
        <item name="colorControlNormal">@color/white</item>
        <item name="titleTextColor">@color/white</item>
    </style>

    <style name="simpletooltip_default" parent="Body2">
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="CheckBoxStyle" parent="Theme.AppCompat.Light">
        <item name="colorControlNormal">@color/black_40</item>
        <item name="colorControlActivated">@color/colorPrimary</item>
    </style>

    <style name="CheckBoxPayment" parent="CheckBoxStyle">
        <item name="checkedIcon">@drawable/payment_checkbox_selector</item>
    </style>

    <style name="Theme.AppCompat.Translucent" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="android:background">@color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@android:style/Animation</item>
        <item name="android:windowOptOutEdgeToEdgeEnforcement">true</item>
    </style>

    <style name="OtpTextInputLayout" parent="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense">
        <item name="boxStrokeColor">@color/otp_field_stroke_color</item>
        <item name="boxBackgroundColor">@color/otp_field_bg_color</item>
        <item name="boxBackgroundMode">outline</item>
    </style>

    <style name="AppTheme.FullScreenDialog" parent="Theme.MaterialComponents.Light.Dialog">
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorPrimary</item>
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowBackground">@android:color/white</item>
    </style>

    <style name="BottomNavigationView" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textStyle">bold</item>
    </style>

    <style name="BottomNavigationView.Active" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textStyle">bold</item>
    </style>

    <style name="PaymentFilterChoiceChipStyle">
        <item name="android:textAppearance">@style/Body2</item>
        <item name="chipBackgroundColor">@color/payment_chip_background_tint</item>
        <item name="chipStrokeColor">@color/payment_chip_stroke_tint</item>
        <item name="chipStrokeWidth">1dp</item>
        <item name="android:textColor">@color/payment_chip_text_tint</item>
        <item name="android:checkable">true</item>
        <item name="android:clickable">true</item>
    </style>

    <style name="chipText" parent="TextAppearance.MaterialComponents.Chip">
        <item name="android:textSize">@dimen/dimen_14sp</item>
        <item name="android:textAppearance">@style/SubHeading1</item>
        <item name="fontFamily">@font/roboto_bold</item>
    </style>

    <style name="CardMaterialCalendarTheme" parent="ThemeOverlay.MaterialComponents.MaterialCalendar">
        <!-- just override the colors used in the default style -->
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorSurface">@color/white</item>
        <item name="colorOnSurface">@color/greySecondary</item>
    </style>

    <style name="MaterialCalendarTheme" parent="ThemeOverlay.MaterialComponents.MaterialCalendar">
        <!-- just override the colors used in the default style -->
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorPrimary">@color/colorPrimary</item>
    </style>

    <style name="FilterTabLayoutTextAppearance" parent="TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <style name="TabLayout_Theme" parent="@style/BukuTheme">
        <item name="android:singleLine">true</item>
    </style>

    <style name="CommonBottomSheetDialogStyle" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="android:colorBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.8</item>
    </style>
    
</resources>
